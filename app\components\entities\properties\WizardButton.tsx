import { useState } from "react";
import { PropertyWithDetails } from "~/utils/db/entities/entities.db.server";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import Modal from "~/components/ui/modals/Modal";
import EmbedWizardWrapper from "./EmbedWizardWrapper";

interface Props {
  property: PropertyWithDetails;
  currentValue?: string | number | Date | boolean;
  onValueChange?: (value: string | number | Date | boolean | undefined | null) => void;
  className?: string;
}

export default function WizardButton({ property, currentValue, onValueChange, className }: Props) {
  const [showModal, setShowModal] = useState(false);

  // Don't render if no wizard is assigned to this property
  if (!property.wizardId) {
    return null;
  }



  return (
    <>
      <ButtonSecondary
        onClick={() => setShowModal(true)}
        className={className}
      >
        Run Wizard
      </ButtonSecondary>

      <Modal
        open={showModal}
        setOpen={setShowModal}
        size="xl"
      >
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Wizard: {property.wizardId}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Property: {property.title}
            </p>
          </div>

          <div className="border-t border-gray-200 pt-4">
            <EmbedWizardWrapper
              property={property}
              wizardId={property.wizardId}
              currentValue={currentValue}
              onValueChange={onValueChange}
              onClose={() => setShowModal(false)}
            />
          </div>
        </div>
      </Modal>
    </>
  );
}
