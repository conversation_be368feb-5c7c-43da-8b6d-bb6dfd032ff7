import { useEffect, useRef, useState } from "react";
import { PropertyWithDetails } from "~/utils/db/entities/entities.db.server";

interface Props {
  property: PropertyWithDetails;
  wizardId: string;
  currentValue?: string | number | Date | boolean;
  onValueChange?: (value: string | number | Date | boolean | undefined | null) => void;
  onClose?: () => void;
}

export default function EmbedWizardWrapper({ 
  property, 
  wizardId, 
  currentValue, 
  onValueChange, 
  onClose 
}: Props) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadEmbedWizard = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if the embed script is already loaded
        if (typeof window !== 'undefined' && containerRef.current) {
          // Try to load the embed.js script dynamically
          const script = document.createElement('script');
          script.src = '/embed.js';
          script.async = true;
          
          script.onload = () => {
            // Once the script is loaded, try to initialize the widget
            try {
              // The embed.js file contains a widget function that we can call
              // We'll pass the wizard configuration and callbacks
              const widgetConfig = {
                widgetId: wizardId,
                apiUrl: window.location.origin,
                disabled: false,
                options: {
                  property: property,
                  currentValue: currentValue,
                  onValueChange: onValueChange,
                },
                verbose: true
              };

              // Mount the widget to our container
              if (containerRef.current && (window as any).SaasRockWidget) {
                (window as any).SaasRockWidget.render(containerRef.current, widgetConfig);
              } else {
                // Fallback: create a simple form interface
                createFallbackInterface();
              }
              
              setIsLoading(false);
            } catch (err) {
              console.error('Error initializing embed wizard:', err);
              setError('Failed to initialize wizard');
              createFallbackInterface();
              setIsLoading(false);
            }
          };

          script.onerror = () => {
            console.error('Failed to load embed.js');
            setError('Failed to load wizard script');
            createFallbackInterface();
            setIsLoading(false);
          };

          document.head.appendChild(script);

          // Cleanup function
          return () => {
            if (document.head.contains(script)) {
              document.head.removeChild(script);
            }
          };
        }
      } catch (err) {
        console.error('Error loading embed wizard:', err);
        setError('Failed to load wizard');
        createFallbackInterface();
        setIsLoading(false);
      }
    };

    const createFallbackInterface = () => {
      if (!containerRef.current) return;

      // Create a simple fallback interface
      containerRef.current.innerHTML = `
        <div class="p-4 border rounded-md bg-gray-50">
          <h3 class="text-lg font-medium mb-4">Wizard: ${wizardId}</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Property: ${property.title}
              </label>
              <input 
                type="text" 
                id="wizard-input-${wizardId}"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value="${currentValue || ''}"
                placeholder="Enter value..."
              />
            </div>
            <div class="flex justify-end space-x-2">
              <button 
                type="button" 
                id="wizard-cancel-${wizardId}"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button 
                type="button" 
                id="wizard-apply-${wizardId}"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      `;

      // Add event listeners for the fallback interface
      const input = containerRef.current.querySelector(`#wizard-input-${wizardId}`) as HTMLInputElement;
      const cancelBtn = containerRef.current.querySelector(`#wizard-cancel-${wizardId}`);
      const applyBtn = containerRef.current.querySelector(`#wizard-apply-${wizardId}`);

      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          onClose?.();
        });
      }

      if (applyBtn && input) {
        applyBtn.addEventListener('click', () => {
          const value = input.value;
          if (onValueChange) {
            // Try to convert the value to the appropriate type based on property type
            let convertedValue: string | number | Date | boolean | null = value;
            
            if (property.type === 2) { // NUMBER
              convertedValue = value ? parseFloat(value) : null;
            } else if (property.type === 3) { // DATE
              convertedValue = value ? new Date(value) : null;
            } else if (property.type === 4) { // BOOLEAN
              convertedValue = value.toLowerCase() === 'true';
            }
            
            onValueChange(convertedValue);
          }
          onClose?.();
        });
      }
    };

    loadEmbedWizard();
  }, [wizardId, property, currentValue, onValueChange, onClose]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading wizard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 border border-red-300 rounded-md bg-red-50">
        <div className="text-red-700">
          <strong>Error:</strong> {error}
        </div>
        <button 
          onClick={onClose}
          className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200"
        >
          Close
        </button>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef} 
      className="wizard-container"
      style={{ minHeight: '200px' }}
    />
  );
}
