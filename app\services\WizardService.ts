import { PropertyWithDetails } from "~/utils/db/entities/entities.db.server";

export interface WizardConfig {
  id: string;
  name: string;
  description?: string;
}

export interface WizardExecutionContext {
  property: PropertyWithDetails;
  currentValue?: string | number | Date | boolean;
  onValueChange?: (value: string | number | Date | boolean | undefined | null) => void;
}

export class WizardService {
  private static wizards: Map<string, WizardConfig> = new Map();

  /**
   * Register a wizard configuration
   */
  static registerWizard(config: WizardConfig) {
    this.wizards.set(config.id, config);
  }

  /**
   * Get a wizard configuration by ID
   */
  static getWizard(wizardId: string): WizardConfig | undefined {
    return this.wizards.get(wizardId);
  }

  /**
   * Get all registered wizards
   */
  static getAllWizards(): WizardConfig[] {
    return Array.from(this.wizards.values());
  }

  /**
   * Check if a wizard exists
   */
  static hasWizard(wizardId: string): boolean {
    return this.wizards.has(wizardId);
  }

  /**
   * Execute a wizard for a property
   */
  static async executeWizard(wizardId: string, context: WizardExecutionContext): Promise<void> {
    const wizard = this.getWizard(wizardId);
    if (!wizard) {
      console.warn(`Wizard with ID "${wizardId}" not found`);
      return;
    }

    try {
      // Load and execute the embed.js wizard
      await this.loadAndExecuteEmbedWizard(wizardId, context);
    } catch (error) {
      console.error(`Error executing wizard "${wizardId}":`, error);
      throw error;
    }
  }

  /**
   * Load and execute the embed.js wizard
   */
  private static async loadAndExecuteEmbedWizard(wizardId: string, context: WizardExecutionContext): Promise<void> {
    // This method will be responsible for loading the embed.js file
    // and executing the wizard functionality
    
    // For now, we'll create a placeholder that can be extended
    // to integrate with the actual embed.js wizard implementation
    
    if (typeof window !== 'undefined') {
      // Client-side execution
      const embedScript = await this.loadEmbedScript();
      if (embedScript && embedScript.executeWizard) {
        await embedScript.executeWizard(wizardId, {
          property: context.property,
          currentValue: context.currentValue,
          onValueChange: context.onValueChange,
        });
      }
    }
  }

  /**
   * Load the embed.js script dynamically
   */
  private static async loadEmbedScript(): Promise<any> {
    try {
      // Dynamically import the embed.js file
      // This assumes the embed.js file exports the necessary functions
      const embedModule = await import('/embed.js');
      return embedModule;
    } catch (error) {
      console.error('Failed to load embed.js:', error);
      return null;
    }
  }

  /**
   * Initialize default wizards
   */
  static initializeDefaultWizards() {
    // Register any default wizards here
    this.registerWizard({
      id: 'default',
      name: 'Default Wizard',
      description: 'A basic wizard implementation',
    });
  }
}

// Initialize default wizards when the service is loaded
WizardService.initializeDefaultWizards();
