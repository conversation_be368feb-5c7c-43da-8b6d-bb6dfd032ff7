(function(H){typeof define=="function"&&define.amd?define(H):H()})(function(){"use strict";var jr;var H,x,Re,F,We,je,ve,we,ye,xe,Y={},Le=[],Or=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,ie=Array.isArray;function W(e,r){for(var t in r)e[t]=r[t];return e}function Ue(e){var r=e.parentNode;r&&r.removeChild(e)}function ke(e,r,t){var o,n,a,i={};for(a in r)a=="key"?o=r[a]:a=="ref"?n=r[a]:i[a]=r[a];if(arguments.length>2&&(i.children=arguments.length>3?H.call(arguments,2):t),typeof e=="function"&&e.defaultProps!=null)for(a in e.defaultProps)i[a]===void 0&&(i[a]=e.defaultProps[a]);return X(e,i,o,n,null)}function X(e,r,t,o,n){var a={type:e,props:r,key:t,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:n??++Re,__i:-1,__u:0};return n==null&&x.vnode!=null&&x.vnode(a),a}function O(e){return e.children}function j(e,r){this.props=e,this.context=r}function G(e,r){if(r==null)return e.__?G(e.__,e.__i+1):null;for(var t;r<e.__k.length;r++)if((t=e.__k[r])!=null&&t.__e!=null)return t.__e;return typeof e.type=="function"?G(e):null}function He(e){var r,t;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,r=0;r<e.__k.length;r++)if((t=e.__k[r])!=null&&t.__e!=null){e.__e=e.__c.base=t.__e;break}return He(e)}}function Oe(e){(!e.__d&&(e.__d=!0)&&F.push(e)&&!se.__r++||We!==x.debounceRendering)&&((We=x.debounceRendering)||je)(se)}function se(){var e,r,t,o,n,a,i,c;for(F.sort(ve);e=F.shift();)e.__d&&(r=F.length,o=void 0,a=(n=(t=e).__v).__e,i=[],c=[],t.__P&&((o=W({},n)).__v=n.__v+1,x.vnode&&x.vnode(o),Ce(t.__P,o,n,t.__n,t.__P.namespaceURI,32&n.__u?[a]:null,i,a??G(n),!!(32&n.__u),c),o.__v=n.__v,o.__.__k[o.__i]=o,Ge(i,o,c),o.__e!=a&&He(o)),F.length>r&&F.sort(ve));se.__r=0}function De(e,r,t,o,n,a,i,c,d,l,u){var s,h,f,S,b,v=o&&o.__k||Le,w=r.length;for(t.__d=d,Dr(t,r,v),d=t.__d,s=0;s<w;s++)(f=t.__k[s])!=null&&typeof f!="boolean"&&typeof f!="function"&&(h=f.__i===-1?Y:v[f.__i]||Y,f.__i=s,Ce(e,f,h,n,a,i,c,d,l,u),S=f.__e,f.ref&&h.ref!=f.ref&&(h.ref&&Se(h.ref,null,f),u.push(f.ref,f.__c||S,f)),b==null&&S!=null&&(b=S),65536&f.__u||h.__k===f.__k?(d&&!d.isConnected&&(d=G(h)),d=Ve(f,d,e)):typeof f.type=="function"&&f.__d!==void 0?d=f.__d:S&&(d=S.nextSibling),f.__d=void 0,f.__u&=-196609);t.__d=d,t.__e=b}function Dr(e,r,t){var o,n,a,i,c,d=r.length,l=t.length,u=l,s=0;for(e.__k=[],o=0;o<d;o++)i=o+s,(n=e.__k[o]=(n=r[o])==null||typeof n=="boolean"||typeof n=="function"?null:typeof n=="string"||typeof n=="number"||typeof n=="bigint"||n.constructor==String?X(null,n,null,null,null):ie(n)?X(O,{children:n},null,null,null):n.constructor===void 0&&n.__b>0?X(n.type,n.props,n.key,n.ref?n.ref:null,n.__v):n)!=null?(n.__=e,n.__b=e.__b+1,c=Vr(n,t,i,u),n.__i=c,a=null,c!==-1&&(u--,(a=t[c])&&(a.__u|=131072)),a==null||a.__v===null?(c==-1&&s--,typeof n.type!="function"&&(n.__u|=65536)):c!==i&&(c===i+1?s++:c>i?u>d-i?s+=c-i:s--:c<i?c==i-1&&(s=c-i):s=0,c!==o+s&&(n.__u|=65536))):(a=t[i])&&a.key==null&&a.__e&&!(131072&a.__u)&&(a.__e==e.__d&&(e.__d=G(a)),Ne(a,a,!1),t[i]=null,u--);if(u)for(o=0;o<l;o++)(a=t[o])!=null&&!(131072&a.__u)&&(a.__e==e.__d&&(e.__d=G(a)),Ne(a,a))}function Ve(e,r,t){var o,n;if(typeof e.type=="function"){for(o=e.__k,n=0;o&&n<o.length;n++)o[n]&&(o[n].__=e,r=Ve(o[n],r,t));return r}e.__e!=r&&(t.insertBefore(e.__e,r||null),r=e.__e);do r=r&&r.nextSibling;while(r!=null&&r.nodeType===8);return r}function L(e,r){return r=r||[],e==null||typeof e=="boolean"||(ie(e)?e.some(function(t){L(t,r)}):r.push(e)),r}function Vr(e,r,t,o){var n=e.key,a=e.type,i=t-1,c=t+1,d=r[t];if(d===null||d&&n==d.key&&a===d.type&&!(131072&d.__u))return t;if(o>(d!=null&&!(131072&d.__u)?1:0))for(;i>=0||c<r.length;){if(i>=0){if((d=r[i])&&!(131072&d.__u)&&n==d.key&&a===d.type)return i;i--}if(c<r.length){if((d=r[c])&&!(131072&d.__u)&&n==d.key&&a===d.type)return c;c++}}return-1}function Be(e,r,t){r[0]==="-"?e.setProperty(r,t??""):e[r]=t==null?"":typeof t!="number"||Or.test(r)?t:t+"px"}function ce(e,r,t,o,n){var a;e:if(r==="style")if(typeof t=="string")e.style.cssText=t;else{if(typeof o=="string"&&(e.style.cssText=o=""),o)for(r in o)t&&r in t||Be(e.style,r,"");if(t)for(r in t)o&&t[r]===o[r]||Be(e.style,r,t[r])}else if(r[0]==="o"&&r[1]==="n")a=r!==(r=r.replace(/(PointerCapture)$|Capture$/i,"$1")),r=r.toLowerCase()in e||r==="onFocusOut"||r==="onFocusIn"?r.toLowerCase().slice(2):r.slice(2),e.l||(e.l={}),e.l[r+a]=t,t?o?t.u=o.u:(t.u=we,e.addEventListener(r,a?xe:ye,a)):e.removeEventListener(r,a?xe:ye,a);else{if(n=="http://www.w3.org/2000/svg")r=r.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(r!="width"&&r!="height"&&r!="href"&&r!="list"&&r!="form"&&r!="tabIndex"&&r!="download"&&r!="rowSpan"&&r!="colSpan"&&r!="role"&&r in e)try{e[r]=t??"";break e}catch{}typeof t=="function"||(t==null||t===!1&&r[4]!=="-"?e.removeAttribute(r):e.setAttribute(r,t))}}function Fe(e){return function(r){if(this.l){var t=this.l[r.type+e];if(r.t==null)r.t=we++;else if(r.t<t.u)return;return t(x.event?x.event(r):r)}}}function Ce(e,r,t,o,n,a,i,c,d,l){var u,s,h,f,S,b,v,w,C,z,M,P,B,p,_,g=r.type;if(r.constructor!==void 0)return null;128&t.__u&&(d=!!(32&t.__u),a=[c=r.__e=t.__e]),(u=x.__b)&&u(r);e:if(typeof g=="function")try{if(w=r.props,C=(u=g.contextType)&&o[u.__c],z=u?C?C.props.value:u.__:o,t.__c?v=(s=r.__c=t.__c).__=s.__E:("prototype"in g&&g.prototype.render?r.__c=s=new g(w,z):(r.__c=s=new j(w,z),s.constructor=g,s.render=Fr),C&&C.sub(s),s.props=w,s.state||(s.state={}),s.context=z,s.__n=o,h=s.__d=!0,s.__h=[],s._sb=[]),s.__s==null&&(s.__s=s.state),g.getDerivedStateFromProps!=null&&(s.__s==s.state&&(s.__s=W({},s.__s)),W(s.__s,g.getDerivedStateFromProps(w,s.__s))),f=s.props,S=s.state,s.__v=r,h)g.getDerivedStateFromProps==null&&s.componentWillMount!=null&&s.componentWillMount(),s.componentDidMount!=null&&s.__h.push(s.componentDidMount);else{if(g.getDerivedStateFromProps==null&&w!==f&&s.componentWillReceiveProps!=null&&s.componentWillReceiveProps(w,z),!s.__e&&(s.shouldComponentUpdate!=null&&s.shouldComponentUpdate(w,s.__s,z)===!1||r.__v===t.__v)){for(r.__v!==t.__v&&(s.props=w,s.state=s.__s,s.__d=!1),r.__e=t.__e,r.__k=t.__k,r.__k.forEach(function(k){k&&(k.__=r)}),M=0;M<s._sb.length;M++)s.__h.push(s._sb[M]);s._sb=[],s.__h.length&&i.push(s);break e}s.componentWillUpdate!=null&&s.componentWillUpdate(w,s.__s,z),s.componentDidUpdate!=null&&s.__h.push(function(){s.componentDidUpdate(f,S,b)})}if(s.context=z,s.props=w,s.__P=e,s.__e=!1,P=x.__r,B=0,"prototype"in g&&g.prototype.render){for(s.state=s.__s,s.__d=!1,P&&P(r),u=s.render(s.props,s.state,s.context),p=0;p<s._sb.length;p++)s.__h.push(s._sb[p]);s._sb=[]}else do s.__d=!1,P&&P(r),u=s.render(s.props,s.state,s.context),s.state=s.__s;while(s.__d&&++B<25);s.state=s.__s,s.getChildContext!=null&&(o=W(W({},o),s.getChildContext())),h||s.getSnapshotBeforeUpdate==null||(b=s.getSnapshotBeforeUpdate(f,S)),De(e,ie(_=u!=null&&u.type===O&&u.key==null?u.props.children:u)?_:[_],r,t,o,n,a,i,c,d,l),s.base=r.__e,r.__u&=-161,s.__h.length&&i.push(s),v&&(s.__E=s.__=null)}catch(k){r.__v=null,d||a!=null?(r.__e=c,r.__u|=d?160:32,a[a.indexOf(c)]=null):(r.__e=t.__e,r.__k=t.__k),x.__e(k,r,t)}else a==null&&r.__v===t.__v?(r.__k=t.__k,r.__e=t.__e):r.__e=Br(t.__e,r,t,o,n,a,i,d,l);(u=x.diffed)&&u(r)}function Ge(e,r,t){r.__d=void 0;for(var o=0;o<t.length;o++)Se(t[o],t[++o],t[++o]);x.__c&&x.__c(r,e),e.some(function(n){try{e=n.__h,n.__h=[],e.some(function(a){a.call(n)})}catch(a){x.__e(a,n.__v)}})}function Br(e,r,t,o,n,a,i,c,d){var l,u,s,h,f,S,b,v=t.props,w=r.props,C=r.type;if(C==="svg"?n="http://www.w3.org/2000/svg":C==="math"?n="http://www.w3.org/1998/Math/MathML":n||(n="http://www.w3.org/1999/xhtml"),a!=null){for(l=0;l<a.length;l++)if((f=a[l])&&"setAttribute"in f==!!C&&(C?f.localName===C:f.nodeType===3)){e=f,a[l]=null;break}}if(e==null){if(C===null)return document.createTextNode(w);e=document.createElementNS(n,C,w.is&&w),a=null,c=!1}if(C===null)v===w||c&&e.data===w||(e.data=w);else{if(a=a&&H.call(e.childNodes),v=t.props||Y,!c&&a!=null)for(v={},l=0;l<e.attributes.length;l++)v[(f=e.attributes[l]).name]=f.value;for(l in v)if(f=v[l],l!="children"){if(l=="dangerouslySetInnerHTML")s=f;else if(l!=="key"&&!(l in w)){if(l=="value"&&"defaultValue"in w||l=="checked"&&"defaultChecked"in w)continue;ce(e,l,null,f,n)}}for(l in w)f=w[l],l=="children"?h=f:l=="dangerouslySetInnerHTML"?u=f:l=="value"?S=f:l=="checked"?b=f:l==="key"||c&&typeof f!="function"||v[l]===f||ce(e,l,f,v[l],n);if(u)c||s&&(u.__html===s.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),r.__k=[];else if(s&&(e.innerHTML=""),De(e,ie(h)?h:[h],r,t,o,C==="foreignObject"?"http://www.w3.org/1999/xhtml":n,a,i,a?a[0]:t.__k&&G(t,0),c,d),a!=null)for(l=a.length;l--;)a[l]!=null&&Ue(a[l]);c||(l="value",S!==void 0&&(S!==e[l]||C==="progress"&&!S||C==="option"&&S!==v[l])&&ce(e,l,S,v[l],n),l="checked",b!==void 0&&b!==e[l]&&ce(e,l,b,v[l],n))}return e}function Se(e,r,t){try{typeof e=="function"?e(r):e.current=r}catch(o){x.__e(o,t)}}function Ne(e,r,t){var o,n;if(x.unmount&&x.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||Se(o,null,r)),(o=e.__c)!=null){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(a){x.__e(a,r)}o.base=o.__P=null}if(o=e.__k)for(n=0;n<o.length;n++)o[n]&&Ne(o[n],r,t||typeof e.type!="function");t||e.__e==null||Ue(e.__e),e.__c=e.__=e.__e=e.__d=void 0}function Fr(e,r,t){return this.constructor(e,t)}function Gr(e,r,t){var o,n,a,i;x.__&&x.__(e,r),n=(o=!1)?null:r.__k,a=[],i=[],Ce(r,e=r.__k=ke(O,null,[e]),n||Y,Y,r.namespaceURI,n?null:r.firstChild?H.call(r.childNodes):null,a,n?n.__e:r.firstChild,o,i),Ge(a,e,i)}function qr(e,r,t){var o,n,a,i,c=W({},e.props);for(a in e.type&&e.type.defaultProps&&(i=e.type.defaultProps),r)a=="key"?o=r[a]:a=="ref"?n=r[a]:c[a]=r[a]===void 0&&i!==void 0?i[a]:r[a];return arguments.length>2&&(c.children=arguments.length>3?H.call(arguments,2):t),X(e.type,c,o||e.key,n||e.ref,null)}H=Le.slice,x={__e:function(e,r,t,o){for(var n,a,i;r=r.__;)if((n=r.__c)&&!n.__)try{if((a=n.constructor)&&a.getDerivedStateFromError!=null&&(n.setState(a.getDerivedStateFromError(e)),i=n.__d),n.componentDidCatch!=null&&(n.componentDidCatch(e,o||{}),i=n.__d),i)return n.__E=n}catch(c){e=c}throw e}},Re=0,j.prototype.setState=function(e,r){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=W({},this.state),typeof e=="function"&&(e=e(W({},t),this.props)),e&&W(t,e),e!=null&&this.__v&&(r&&this._sb.push(r),Oe(this))},j.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Oe(this))},j.prototype.render=O,F=[],je=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,ve=function(e,r){return e.__v.__b-r.__v.__b},se.__r=0,we=0,ye=Fe(!1),xe=Fe(!0);function qe(e){var r,t,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var n=e.length;for(r=0;r<n;r++)e[r]&&(t=qe(e[r]))&&(o&&(o+=" "),o+=t)}else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function T(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=qe(e))&&(o&&(o+=" "),o+=r);return o}var J,$,Ee,Ze,K=0,Ye=[],le=[],I=x,Xe=I.__b,Je=I.__r,Ke=I.diffed,Qe=I.__c,er=I.unmount,rr=I.__;function Ae(e,r){I.__h&&I.__h($,e,K||r),K=0;var t=$.__H||($.__H={__:[],__h:[]});return e>=t.__.length&&t.__.push({__V:le}),t.__[e]}function R(e){return K=1,Zr(ir,e)}function Zr(e,r,t){var o=Ae(J++,2);if(o.t=e,!o.__c&&(o.__=[t?t(r):ir(void 0,r),function(c){var d=o.__N?o.__N[0]:o.__[0],l=o.t(d,c);d!==l&&(o.__N=[l,o.__[1]],o.__c.setState({}))}],o.__c=$,!$.u)){var n=function(c,d,l){if(!o.__c.__H)return!0;var u=o.__c.__H.__.filter(function(h){return!!h.__c});if(u.every(function(h){return!h.__N}))return!a||a.call(this,c,d,l);var s=!1;return u.forEach(function(h){if(h.__N){var f=h.__[0];h.__=h.__N,h.__N=void 0,f!==h.__[0]&&(s=!0)}}),!(!s&&o.__c.props===c)&&(!a||a.call(this,c,d,l))};$.u=!0;var a=$.shouldComponentUpdate,i=$.componentWillUpdate;$.componentWillUpdate=function(c,d,l){if(this.__e){var u=a;a=void 0,n(c,d,l),a=u}i&&i.call(this,c,d,l)},$.shouldComponentUpdate=n}return o.__N||o.__}function de(e,r){var t=Ae(J++,3);!I.__s&&ar(t.__H,r)&&(t.__=e,t.i=r,$.__H.__h.push(t))}function ue(e){return K=5,tr(function(){return{current:e}},[])}function tr(e,r){var t=Ae(J++,7);return ar(t.__H,r)?(t.__V=e(),t.i=r,t.__h=e,t.__V):t.__}function or(e,r){return K=8,tr(function(){return e},r)}function Yr(){for(var e;e=Ye.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(fe),e.__H.__h.forEach(ze),e.__H.__h=[]}catch(r){e.__H.__h=[],I.__e(r,e.__v)}}I.__b=function(e){$=null,Xe&&Xe(e)},I.__=function(e,r){e&&r.__k&&r.__k.__m&&(e.__m=r.__k.__m),rr&&rr(e,r)},I.__r=function(e){Je&&Je(e),J=0;var r=($=e.__c).__H;r&&(Ee===$?(r.__h=[],$.__h=[],r.__.forEach(function(t){t.__N&&(t.__=t.__N),t.__V=le,t.__N=t.i=void 0})):(r.__h.forEach(fe),r.__h.forEach(ze),r.__h=[],J=0)),Ee=$},I.diffed=function(e){Ke&&Ke(e);var r=e.__c;r&&r.__H&&(r.__H.__h.length&&(Ye.push(r)!==1&&Ze===I.requestAnimationFrame||((Ze=I.requestAnimationFrame)||Xr)(Yr)),r.__H.__.forEach(function(t){t.i&&(t.__H=t.i),t.__V!==le&&(t.__=t.__V),t.i=void 0,t.__V=le})),Ee=$=null},I.__c=function(e,r){r.some(function(t){try{t.__h.forEach(fe),t.__h=t.__h.filter(function(o){return!o.__||ze(o)})}catch(o){r.some(function(n){n.__h&&(n.__h=[])}),r=[],I.__e(o,t.__v)}}),Qe&&Qe(e,r)},I.unmount=function(e){er&&er(e);var r,t=e.__c;t&&t.__H&&(t.__H.__.forEach(function(o){try{fe(o)}catch(n){r=n}}),t.__H=void 0,r&&I.__e(r,t.__v))};var nr=typeof requestAnimationFrame=="function";function Xr(e){var r,t=function(){clearTimeout(o),nr&&cancelAnimationFrame(r),setTimeout(e)},o=setTimeout(t,100);nr&&(r=requestAnimationFrame(t))}function fe(e){var r=$,t=e.__c;typeof t=="function"&&(e.__c=void 0,t()),$=r}function ze(e){var r=$;e.__c=e.__(),$=r}function ar(e,r){return!e||e.length!==r.length||r.some(function(t,o){return t!==e[o]})}function ir(e,r){return typeof r=="function"?r(e):r}function sr(e,r){for(var t in r)e[t]=r[t];return e}function cr(e,r){for(var t in e)if(t!=="__source"&&!(t in r))return!0;for(var o in r)if(o!=="__source"&&e[o]!==r[o])return!0;return!1}function lr(e,r){this.props=e,this.context=r}(lr.prototype=new j).isPureReactComponent=!0,lr.prototype.shouldComponentUpdate=function(e,r){return cr(this.props,e)||cr(this.state,r)};var dr=x.__b;x.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),dr&&dr(e)};var Jr=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function pe(e){function r(t){var o=sr({},t);return delete o.ref,e(o,t.ref||null)}return r.$$typeof=Jr,r.render=r,r.prototype.isReactComponent=r.__f=!0,r.displayName="ForwardRef("+(e.displayName||e.name)+")",r}var ur=function(e,r){return e==null?null:L(L(e).map(r))},Q={map:ur,forEach:ur,count:function(e){return e?L(e).length:0},only:function(e){var r=L(e);if(r.length!==1)throw"Children.only";return r[0]},toArray:L},Kr=x.__e;x.__e=function(e,r,t,o){if(e.then){for(var n,a=r;a=a.__;)if((n=a.__c)&&n.__c)return r.__e==null&&(r.__e=t.__e,r.__k=t.__k),n.__c(e,r)}Kr(e,r,t,o)};var fr=x.unmount;function pr(e,r,t){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach(function(o){typeof o.__c=="function"&&o.__c()}),e.__c.__H=null),(e=sr({},e)).__c!=null&&(e.__c.__P===t&&(e.__c.__P=r),e.__c=null),e.__k=e.__k&&e.__k.map(function(o){return pr(o,r,t)})),e}function gr(e,r,t){return e&&t&&(e.__v=null,e.__k=e.__k&&e.__k.map(function(o){return gr(o,r,t)}),e.__c&&e.__c.__P===r&&(e.__e&&t.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=t)),e}function $e(){this.__u=0,this.t=null,this.__b=null}function mr(e){var r=e.__.__c;return r&&r.__a&&r.__a(e)}function ge(){this.u=null,this.o=null}x.unmount=function(e){var r=e.__c;r&&r.__R&&r.__R(),r&&32&e.__u&&(e.type=null),fr&&fr(e)},($e.prototype=new j).__c=function(e,r){var t=r.__c,o=this;o.t==null&&(o.t=[]),o.t.push(t);var n=mr(o.__v),a=!1,i=function(){a||(a=!0,t.__R=null,n?n(c):c())};t.__R=i;var c=function(){if(!--o.__u){if(o.state.__a){var d=o.state.__a;o.__v.__k[0]=gr(d,d.__c.__P,d.__c.__O)}var l;for(o.setState({__a:o.__b=null});l=o.t.pop();)l.forceUpdate()}};o.__u++||32&r.__u||o.setState({__a:o.__b=o.__v.__k[0]}),e.then(i,i)},$e.prototype.componentWillUnmount=function(){this.t=[]},$e.prototype.render=function(e,r){if(this.__b){if(this.__v.__k){var t=document.createElement("div"),o=this.__v.__k[0].__c;this.__v.__k[0]=pr(this.__b,t,o.__O=o.__P)}this.__b=null}var n=r.__a&&ke(O,null,e.fallback);return n&&(n.__u&=-33),[ke(O,null,r.__a?null:e.children),n]};var hr=function(e,r,t){if(++t[1]===t[0]&&e.o.delete(r),e.props.revealOrder&&(e.props.revealOrder[0]!=="t"||!e.o.size))for(t=e.u;t;){for(;t.length>3;)t.pop()();if(t[1]<t[0])break;e.u=t=t[2]}};(ge.prototype=new j).__a=function(e){var r=this,t=mr(r.__v),o=r.o.get(e);return o[0]++,function(n){var a=function(){r.props.revealOrder?(o.push(n),hr(r,e,o)):n()};t?t(a):a()}},ge.prototype.render=function(e){this.u=null,this.o=new Map;var r=L(e.children);e.revealOrder&&e.revealOrder[0]==="b"&&r.reverse();for(var t=r.length;t--;)this.o.set(r[t],this.u=[1,0,this.u]);return e.children},ge.prototype.componentDidUpdate=ge.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(r,t){hr(e,t,r)})};var _r=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,Qr=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,et=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,rt=/[A-Z0-9]/g,tt=typeof document<"u",ot=function(e){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(e)};j.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(j.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(r){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:r})}})});var br=x.event;function nt(){}function at(){return this.cancelBubble}function it(){return this.defaultPrevented}x.event=function(e){return br&&(e=br(e)),e.persist=nt,e.isPropagationStopped=at,e.isDefaultPrevented=it,e.nativeEvent=e};var st={enumerable:!1,configurable:!0,get:function(){return this.class}},vr=x.vnode;x.vnode=function(e){typeof e.type=="string"&&function(r){var t=r.props,o=r.type,n={};for(var a in t){var i=t[a];if(!(a==="value"&&"defaultValue"in t&&i==null||tt&&a==="children"&&o==="noscript"||a==="class"||a==="className")){var c=a.toLowerCase();a==="defaultValue"&&"value"in t&&t.value==null?a="value":a==="download"&&i===!0?i="":c==="translate"&&i==="no"?i=!1:c==="ondoubleclick"?a="ondblclick":c!=="onchange"||o!=="input"&&o!=="textarea"||ot(t.type)?c==="onfocus"?a="onfocusin":c==="onblur"?a="onfocusout":et.test(a)?a=c:o.indexOf("-")===-1&&Qr.test(a)?a=a.replace(rt,"-$&").toLowerCase():i===null&&(i=void 0):c=a="oninput",c==="oninput"&&n[a=c]&&(a="oninputCapture"),n[a]=i}}o=="select"&&n.multiple&&Array.isArray(n.value)&&(n.value=L(t.children).forEach(function(d){d.props.selected=n.value.indexOf(d.props.value)!=-1})),o=="select"&&n.defaultValue!=null&&(n.value=L(t.children).forEach(function(d){d.props.selected=n.multiple?n.defaultValue.indexOf(d.props.value)!=-1:n.defaultValue==d.props.value})),t.class&&!t.className?(n.class=t.class,Object.defineProperty(n,"className",st)):(t.className&&!t.class||t.class&&t.className)&&(n.class=n.className=t.className),r.props=n}(e),e.$$typeof=_r,vr&&vr(e)};var wr=x.__r;x.__r=function(e){wr&&wr(e),e.__c};var yr=x.diffed;x.diffed=function(e){yr&&yr(e);var r=e.props,t=e.__e;t!=null&&e.type==="textarea"&&"value"in r&&r.value!==t.value&&(t.value=r.value==null?"":r.value)};function ee(e){return!!e&&e.$$typeof===_r}function xr(e){return ee(e)?qr.apply(null,arguments):e}var ct=0;function m(e,r,t,o,n,a){r||(r={});var i,c,d=r;if("ref"in d)for(c in d={},r)c=="ref"?i=r[c]:d[c]=r[c];var l={type:e,props:d,key:t,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--ct,__i:-1,__u:0,__source:n,__self:a};if(typeof e=="function"&&(i=e.defaultProps))for(c in i)d[c]===void 0&&(d[c]=i[c]);return x.vnode&&x.vnode(l),l}function lt({title:e="Error",text:r="",children:t}){return m("div",{className:"not-prose rounded-md border border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900",children:m("div",{className:"rounded-md p-4",children:m("div",{className:"flex",children:[m("div",{className:"flex-shrink-0",children:m("svg",{className:"h-5 w-5 text-red-400 dark:text-red-300",viewBox:"0 0 20 20",fill:"currentColor",children:m("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),m("div",{className:"ml-3",children:[m("h3",{className:"text-sm font-medium leading-5 text-red-800 dark:text-red-100",children:e}),m("div",{className:"mt-2 text-sm leading-5 text-red-700 dark:text-red-300",children:m("div",{children:[r," ",t]})})]})]})})})}function dt(e,r){typeof e=="function"?e(r):e!=null&&(e.current=r)}function ut(...e){return r=>e.forEach(t=>dt(t,r))}var kr=pe((e,r)=>{const{children:t,...o}=e,n=Q.toArray(t),a=n.find(pt);if(a){const i=a.props.children,c=n.map(d=>d===a?Q.count(i)>1?Q.only(null):ee(i)?i.props.children:null:d);return m(Me,{...o,ref:r,children:ee(i)?xr(i,void 0,c):null})}return m(Me,{...o,ref:r,children:t})});kr.displayName="Slot";var Me=pe((e,r)=>{const{children:t,...o}=e;if(ee(t)){const n=mt(t);return xr(t,{...gt(o,t.props),ref:r?ut(r,n):n})}return Q.count(t)>1?Q.only(null):null});Me.displayName="SlotClone";var ft=({children:e})=>m(O,{children:e});function pt(e){return ee(e)&&e.type===ft}function gt(e,r){const t={...r};for(const o in r){const n=e[o],a=r[o];/^on[A-Z]/.test(o)?n&&a?t[o]=(...c)=>{a(...c),n(...c)}:n&&(t[o]=n):o==="style"?t[o]={...n,...a}:o==="className"&&(t[o]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}function mt(e){var o,n;let r=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(r=(n=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:n.get,t=r&&"isReactWarning"in r&&r.isReactWarning,t?e.props.ref:e.props.ref||e.ref)}function Cr(e){var r,t,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e))for(r=0;r<e.length;r++)e[r]&&(t=Cr(e[r]))&&(o&&(o+=" "),o+=t);else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}function ht(){for(var e,r,t=0,o="";t<arguments.length;)(e=arguments[t++])&&(r=Cr(e))&&(o&&(o+=" "),o+=r);return o}const Sr=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,Nr=ht,_t=(e,r)=>t=>{var o;if((r==null?void 0:r.variants)==null)return Nr(e,t==null?void 0:t.class,t==null?void 0:t.className);const{variants:n,defaultVariants:a}=r,i=Object.keys(n).map(l=>{const u=t==null?void 0:t[l],s=a==null?void 0:a[l];if(u===null)return null;const h=Sr(u)||Sr(s);return n[l][h]}),c=t&&Object.entries(t).reduce((l,u)=>{let[s,h]=u;return h===void 0||(l[s]=h),l},{}),d=r==null||(o=r.compoundVariants)===null||o===void 0?void 0:o.reduce((l,u)=>{let{class:s,className:h,...f}=u;return Object.entries(f).every(S=>{let[b,v]=S;return Array.isArray(v)?v.includes({...a,...c}[b]):{...a,...c}[b]===v})?[...l,s,h]:l},[]);return Nr(e,i,d,t==null?void 0:t.class,t==null?void 0:t.className)},Pe="-";function bt(e){const r=wt(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;function n(i){const c=i.split(Pe);return c[0]===""&&c.length!==1&&c.shift(),Er(c,r)||vt(i)}function a(i,c){const d=t[i]||[];return c&&o[i]?[...d,...o[i]]:d}return{getClassGroupId:n,getConflictingClassGroupIds:a}}function Er(e,r){var i;if(e.length===0)return r.classGroupId;const t=e[0],o=r.nextPart.get(t),n=o?Er(e.slice(1),o):void 0;if(n)return n;if(r.validators.length===0)return;const a=e.join(Pe);return(i=r.validators.find(({validator:c})=>c(a)))==null?void 0:i.classGroupId}const Ar=/^\[(.+)\]$/;function vt(e){if(Ar.test(e)){const r=Ar.exec(e)[1],t=r==null?void 0:r.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}}function wt(e){const{theme:r,prefix:t}=e,o={nextPart:new Map,validators:[]};return xt(Object.entries(e.classGroups),t).forEach(([a,i])=>{Ie(i,o,a,r)}),o}function Ie(e,r,t,o){e.forEach(n=>{if(typeof n=="string"){const a=n===""?r:zr(r,n);a.classGroupId=t;return}if(typeof n=="function"){if(yt(n)){Ie(n(o),r,t,o);return}r.validators.push({validator:n,classGroupId:t});return}Object.entries(n).forEach(([a,i])=>{Ie(i,zr(r,a),t,o)})})}function zr(e,r){let t=e;return r.split(Pe).forEach(o=>{t.nextPart.has(o)||t.nextPart.set(o,{nextPart:new Map,validators:[]}),t=t.nextPart.get(o)}),t}function yt(e){return e.isThemeGetter}function xt(e,r){return r?e.map(([t,o])=>{const n=o.map(a=>typeof a=="string"?r+a:typeof a=="object"?Object.fromEntries(Object.entries(a).map(([i,c])=>[r+i,c])):a);return[t,n]}):e}function kt(e){if(e<1)return{get:()=>{},set:()=>{}};let r=0,t=new Map,o=new Map;function n(a,i){t.set(a,i),r++,r>e&&(r=0,o=t,t=new Map)}return{get(a){let i=t.get(a);if(i!==void 0)return i;if((i=o.get(a))!==void 0)return n(a,i),i},set(a,i){t.has(a)?t.set(a,i):n(a,i)}}}const $r="!";function Ct(e){const r=e.separator,t=r.length===1,o=r[0],n=r.length;return function(i){const c=[];let d=0,l=0,u;for(let b=0;b<i.length;b++){let v=i[b];if(d===0){if(v===o&&(t||i.slice(b,b+n)===r)){c.push(i.slice(l,b)),l=b+n;continue}if(v==="/"){u=b;continue}}v==="["?d++:v==="]"&&d--}const s=c.length===0?i:i.substring(l),h=s.startsWith($r),f=h?s.substring(1):s,S=u&&u>l?u-l:void 0;return{modifiers:c,hasImportantModifier:h,baseClassName:f,maybePostfixModifierPosition:S}}}function St(e){if(e.length<=1)return e;const r=[];let t=[];return e.forEach(o=>{o[0]==="["?(r.push(...t.sort(),o),t=[]):t.push(o)}),r.push(...t.sort()),r}function Nt(e){return{cache:kt(e.cacheSize),splitModifiers:Ct(e),...bt(e)}}const Et=/\s+/;function At(e,r){const{splitModifiers:t,getClassGroupId:o,getConflictingClassGroupIds:n}=r,a=new Set;return e.trim().split(Et).map(i=>{const{modifiers:c,hasImportantModifier:d,baseClassName:l,maybePostfixModifierPosition:u}=t(i);let s=o(u?l.substring(0,u):l),h=!!u;if(!s){if(!u)return{isTailwindClass:!1,originalClassName:i};if(s=o(l),!s)return{isTailwindClass:!1,originalClassName:i};h=!1}const f=St(c).join(":");return{isTailwindClass:!0,modifierId:d?f+$r:f,classGroupId:s,originalClassName:i,hasPostfixModifier:h}}).reverse().filter(i=>{if(!i.isTailwindClass)return!0;const{modifierId:c,classGroupId:d,hasPostfixModifier:l}=i,u=c+d;return a.has(u)?!1:(a.add(u),n(d,l).forEach(s=>a.add(c+s)),!0)}).reverse().map(i=>i.originalClassName).join(" ")}function zt(){let e=0,r,t,o="";for(;e<arguments.length;)(r=arguments[e++])&&(t=Mr(r))&&(o&&(o+=" "),o+=t);return o}function Mr(e){if(typeof e=="string")return e;let r,t="";for(let o=0;o<e.length;o++)e[o]&&(r=Mr(e[o]))&&(t&&(t+=" "),t+=r);return t}function $t(e,...r){let t,o,n,a=i;function i(d){const l=r.reduce((u,s)=>s(u),e());return t=Nt(l),o=t.cache.get,n=t.cache.set,a=c,c(d)}function c(d){const l=o(d);if(l)return l;const u=At(d,t);return n(d,u),u}return function(){return a(zt.apply(null,arguments))}}function A(e){const r=t=>t[e]||[];return r.isThemeGetter=!0,r}const Pr=/^\[(?:([a-z-]+):)?(.+)\]$/i,Mt=/^\d+\/\d+$/,Pt=new Set(["px","full","screen"]),It=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Tt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Rt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Wt=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,jt=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function U(e){return q(e)||Pt.has(e)||Mt.test(e)}function D(e){return Z(e,"length",Ft)}function q(e){return!!e&&!Number.isNaN(Number(e))}function me(e){return Z(e,"number",q)}function re(e){return!!e&&Number.isInteger(Number(e))}function Lt(e){return e.endsWith("%")&&q(e.slice(0,-1))}function y(e){return Pr.test(e)}function V(e){return It.test(e)}const Ut=new Set(["length","size","percentage"]);function Ht(e){return Z(e,Ut,Ir)}function Ot(e){return Z(e,"position",Ir)}const Dt=new Set(["image","url"]);function Vt(e){return Z(e,Dt,qt)}function Bt(e){return Z(e,"",Gt)}function te(){return!0}function Z(e,r,t){const o=Pr.exec(e);return o?o[1]?typeof r=="string"?o[1]===r:r.has(o[1]):t(o[2]):!1}function Ft(e){return Tt.test(e)&&!Rt.test(e)}function Ir(){return!1}function Gt(e){return Wt.test(e)}function qt(e){return jt.test(e)}function Zt(){const e=A("colors"),r=A("spacing"),t=A("blur"),o=A("brightness"),n=A("borderColor"),a=A("borderRadius"),i=A("borderSpacing"),c=A("borderWidth"),d=A("contrast"),l=A("grayscale"),u=A("hueRotate"),s=A("invert"),h=A("gap"),f=A("gradientColorStops"),S=A("gradientColorStopPositions"),b=A("inset"),v=A("margin"),w=A("opacity"),C=A("padding"),z=A("saturate"),M=A("scale"),P=A("sepia"),B=A("skew"),p=A("space"),_=A("translate"),g=()=>["auto","contain","none"],k=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto",y,r],N=()=>[y,r],oe=()=>["",U,D],he=()=>["auto",q,y],Lr=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],_e=()=>["solid","dashed","dotted","double","none"],Ur=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Te=()=>["start","end","center","between","around","evenly","stretch"],ne=()=>["","0",y],Hr=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ae=()=>[q,me],be=()=>[q,y];return{cacheSize:500,separator:":",theme:{colors:[te],spacing:[U,D],blur:["none","",V,y],brightness:ae(),borderColor:[e],borderRadius:["none","","full",V,y],borderSpacing:N(),borderWidth:oe(),contrast:ae(),grayscale:ne(),hueRotate:be(),invert:ne(),gap:N(),gradientColorStops:[e],gradientColorStopPositions:[Lt,D],inset:E(),margin:E(),opacity:ae(),padding:N(),saturate:ae(),scale:ae(),sepia:ne(),skew:be(),space:N(),translate:N()},classGroups:{aspect:[{aspect:["auto","square","video",y]}],container:["container"],columns:[{columns:[V]}],"break-after":[{"break-after":Hr()}],"break-before":[{"break-before":Hr()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Lr(),y]}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:g()}],"overscroll-x":[{"overscroll-x":g()}],"overscroll-y":[{"overscroll-y":g()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",re,y]}],basis:[{basis:E()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",y]}],grow:[{grow:ne()}],shrink:[{shrink:ne()}],order:[{order:["first","last","none",re,y]}],"grid-cols":[{"grid-cols":[te]}],"col-start-end":[{col:["auto",{span:["full",re,y]},y]}],"col-start":[{"col-start":he()}],"col-end":[{"col-end":he()}],"grid-rows":[{"grid-rows":[te]}],"row-start-end":[{row:["auto",{span:[re,y]},y]}],"row-start":[{"row-start":he()}],"row-end":[{"row-end":he()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",y]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",y]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...Te()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Te(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Te(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[C]}],px:[{px:[C]}],py:[{py:[C]}],ps:[{ps:[C]}],pe:[{pe:[C]}],pt:[{pt:[C]}],pr:[{pr:[C]}],pb:[{pb:[C]}],pl:[{pl:[C]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[p]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[p]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",y,r]}],"min-w":[{"min-w":[y,r,"min","max","fit"]}],"max-w":[{"max-w":[y,r,"none","full","min","max","fit","prose",{screen:[V]},V]}],h:[{h:[y,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[y,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[y,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[y,r,"auto","min","max","fit"]}],"font-size":[{text:["base",V,D]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",me]}],"font-family":[{font:[te]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",y]}],"line-clamp":[{"line-clamp":["none",q,me]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",U,y]}],"list-image":[{"list-image":["none",y]}],"list-style-type":[{list:["none","disc","decimal",y]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[w]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[w]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[..._e(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",U,D]}],"underline-offset":[{"underline-offset":["auto",U,y]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[w]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Lr(),Ot]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Ht]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Vt]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[S]}],"gradient-via-pos":[{via:[S]}],"gradient-to-pos":[{to:[S]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[c]}],"border-w-x":[{"border-x":[c]}],"border-w-y":[{"border-y":[c]}],"border-w-s":[{"border-s":[c]}],"border-w-e":[{"border-e":[c]}],"border-w-t":[{"border-t":[c]}],"border-w-r":[{"border-r":[c]}],"border-w-b":[{"border-b":[c]}],"border-w-l":[{"border-l":[c]}],"border-opacity":[{"border-opacity":[w]}],"border-style":[{border:[..._e(),"hidden"]}],"divide-x":[{"divide-x":[c]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[c]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[w]}],"divide-style":[{divide:_e()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",..._e()]}],"outline-offset":[{"outline-offset":[U,y]}],"outline-w":[{outline:[U,D]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:oe()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[w]}],"ring-offset-w":[{"ring-offset":[U,D]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",V,Bt]}],"shadow-color":[{shadow:[te]}],opacity:[{opacity:[w]}],"mix-blend":[{"mix-blend":[...Ur(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Ur()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[o]}],contrast:[{contrast:[d]}],"drop-shadow":[{"drop-shadow":["","none",V,y]}],grayscale:[{grayscale:[l]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[s]}],saturate:[{saturate:[z]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[d]}],"backdrop-grayscale":[{"backdrop-grayscale":[l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[s]}],"backdrop-opacity":[{"backdrop-opacity":[w]}],"backdrop-saturate":[{"backdrop-saturate":[z]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",y]}],duration:[{duration:be()}],ease:[{ease:["linear","in","out","in-out",y]}],delay:[{delay:be()}],animate:[{animate:["none","spin","ping","pulse","bounce",y]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[M]}],"scale-x":[{"scale-x":[M]}],"scale-y":[{"scale-y":[M]}],rotate:[{rotate:[re,y]}],"translate-x":[{"translate-x":[_]}],"translate-y":[{"translate-y":[_]}],"skew-x":[{"skew-x":[B]}],"skew-y":[{"skew-y":[B]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",y]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",y]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",y]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[U,D,me]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const Yt=$t(Zt);function Tr(...e){return Yt(T(e))}const Xt=_t("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),Rr=pe(({className:e,variant:r,size:t,asChild:o=!1,...n},a)=>m(o?kr:"button",{className:Tr(Xt({variant:r,size:t,className:e})),ref:a,...n}));Rr.displayName="Button";const Wr=pe(({className:e,type:r,...t},o)=>m("input",{type:r,className:Tr("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...t}));Wr.displayName="Input";function Jt({className:e}){return m("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",children:m("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"})})}function Kt({widget:e,widgetData:r,className:t="h-[calc(100vh-100px)]",size:o="lg",isDarkMode:n,error:a,disabled:i,canSubmit:c,onTestWidget:d,onClose:l,onReset:u}){const[s]=R(n||!1),[h]=R((e==null?void 0:e.appearance.theme)||"zinc"),[f,S]=R(""),b=ue(null);function v(w){w.preventDefault(),c&&(S(""),d(f))}return m("div",{className:T(s&&"dark"),children:m("form",{onSubmit:v,className:T(t,"bg-background text-foreground relative flex flex-col overflow-hidden",`theme-${h}`),children:[e&&m("div",{className:T("border-border border-b p-1",o==="full"&&"px-4",o==="lg"&&"px-4",o==="md"&&"px-2",o==="sm"&&"px-2"),children:m("div",{className:"flex items-center justify-between space-x-2",children:[m("div",{children:e&&m("div",{className:"flex items-center space-x-2",children:[e.appearance.logo&&m("img",{src:e.appearance.logo,alt:e.name,className:"h-8 w-8"}),e.name&&m("div",{className:T("text-foreground py-2 text-center font-medium",o==="full"&&"text-base",o==="lg"&&"text-base",o==="md"&&"text-base",o==="sm"&&"text-sm"),children:e.name})]})}),(u||l)&&m("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[u&&m("div",{children:m("button",{type:"button",onClick:u,className:T("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50","hover:bg-accent hover:text-accent-foreground","px-1 py-1"),children:m(Jt,{className:"h-4 w-4"})})}),l&&m("div",{children:m("button",{type:"button",onClick:l,className:T("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50","hover:bg-accent hover:text-accent-foreground","px-1 py-1"),children:m("svg",{className:"h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",children:m("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})})})})]})]})}),m("div",{className:T("mx-auto w-full flex-grow overflow-auto px-4 py-2",o!=="full"&&"lg:max-w-[40rem] xl:max-w-[48rem]"),children:m("div",{className:"flex-1 space-y-2",children:[m("div",{className:"relative flex items-center",children:[m(Wr,{ref:b,disabled:i,autoFocus:!0,type:"text",name:"message",id:"message",autoComplete:"off",placeholder:'Send "test" or "test-error"',value:f,className:"h-11",onChange:w=>S(w.target.value),required:!0}),m("div",{className:"absolute inset-y-0 right-1 top-1 flex",children:m(Rr,{type:"submit",children:"Test"})})]}),m("div",{className:"text-lg font-bold bg-secondary border border-border rounded-md p-4",children:["DATA: ",JSON.stringify({widgetData:r})]}),a&&m("div",{className:T("mx-auto w-full overflow-auto pb-2",o!=="full"&&"lg:max-w-[40rem] xl:max-w-[48rem]",o==="full"&&"px-4"),children:m(lt,{title:"Error",text:a})})]})})]})})}function Qt({className:e}){return m("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:m("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})}function eo({widgetId:e,apiUrl:r,disabled:t,options:o,verbose:n}){const[a,i]=R(!0),[c,d]=R(null),[l,u]=R(),[s,h]=R(!0),[f,S]=R(),b=window.location.pathname;de(()=>{function C(){let z=`${r}/api/widget/${e}`;u(void 0),fetch(z).then(async M=>{const P=await M.json();console.log({data:P}),d(P),i(!1)}).catch(M=>{n&&console.error("[SaasRock.Widget] Error loading widget",{widgetId:e,url:z,error:M.message}),u(l)})}C()},[r,e]);const v=async C=>{const z=new FormData;z.set("action","test-widget"),z.set("content",C),h(!1),u(void 0),fetch(`${r}/api/widget/${e}`,{method:"POST",body:z}).then(async M=>{const P=await M.json();if("error"in P)throw new Error(P.error);S(P)}).catch(M=>{n&&console.error("[SaasRock.Widget] Error sending message",{widgetId:e,apiUrl:r,error:M}),u(M.message),S({error:M.message})}).finally(()=>{h(!0)})},w=()=>{S(void 0)};if(a||!c)return null;if(c.appearance.hiddenInUrls.length>0){const C=c.appearance.hiddenInUrls.find(z=>b.startsWith(z));if(C)return n&&console.log("[SaasRock.Widget] Hidden in URL",{hiddenUrl:C,currentPath:b}),null}return c.appearance.visibleInUrls.length>0&&!c.appearance.visibleInUrls.some(z=>b.startsWith(z))?(n&&console.log("[SaasRock.Widget] Not visible in URL",{visibleUrls:c.appearance.visibleInUrls,currentPath:b}),null):c?m(ro,{widget:c,widgetData:f,disabled:t===!0,onTestWidget:v,canSubmit:!t&&s,onReset:t?void 0:w,options:o,error:l}):null}function ro({widget:e,widgetData:r,disabled:t,onTestWidget:o,canSubmit:n,onReset:a,options:i,error:c}){const[d,l]=R(!1),[u,s]=R(!1);return de(()=>{(i==null?void 0:i.openDelay)!==void 0&&i.openDelay>=0&&setTimeout(()=>{s(!0)},i.openDelay)},[i==null?void 0:i.openDelay]),d?null:m("div",{className:T("z-50",!(i!=null&&i.isPreview)&&"fixed"),children:[m("button",{type:"button",disabled:t,className:T(!(i!=null&&i.isPreview)&&"fixed",e.appearance.position==="bottom-right"&&"bottom-6 right-6",e.appearance.position==="bottom-left"&&"bottom-6 left-6",e.appearance.position==="top-right"&&"right-6 top-6",e.appearance.position==="top-left"&&"left-6 top-6",e.appearance.position==="center"&&"bottom-6 left-6 right-6",`theme-${e.appearance.theme}`,e.appearance.mode==="dark"&&"dark","bg-primary text-primary-foreground hover:bg-primary/90 shadow","rounded-full shadow-md","flex h-14 w-14 items-center justify-center transition-all duration-300 ease-in-out"),onClick:()=>s(!u),children:e.appearance.logo&&!u?m("img",{src:e.appearance.logo,alt:e.name,className:"h-14 w-14 object-contain"}):m(O,{children:u?m(Qt,{className:"h-5 w-5 transition-all duration-300 ease-in-out"}):m("svg",{className:"h-7 w-7 transition-all duration-300 ease-in-out",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",children:m("path",{"fill-rule":"evenodd",d:"M12 1.5a.75.75 0 0 1 .75.75V4.5a.75.75 0 0 1-1.5 0V2.25A.75.75 0 0 1 12 1.5ZM5.636 4.136a.75.75 0 0 1 1.06 0l1.592 1.591a.75.75 0 0 1-1.061 1.06l-1.591-1.59a.75.75 0 0 1 0-1.061Zm12.728 0a.75.75 0 0 1 0 1.06l-1.591 1.592a.75.75 0 0 1-1.06-1.061l1.59-1.591a.75.75 0 0 1 1.061 0Zm-6.816 4.496a.75.75 0 0 1 .82.311l5.228 7.917a.75.75 0 0 1-.777 1.148l-2.097-.43 1.045 3.9a.75.75 0 0 1-1.45.388l-1.044-3.899-1.601 1.42a.75.75 0 0 1-1.247-.606l.569-9.47a.75.75 0 0 1 .554-.68ZM3 10.5a.75.75 0 0 1 .75-.75H6a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 10.5Zm14.25 0a.75.75 0 0 1 .75-.75h2.25a.75.75 0 0 1 0 1.5H18a.75.75 0 0 1-.75-.75Zm-8.962 3.712a.75.75 0 0 1 0 1.061l-1.591 1.591a.75.75 0 1 1-1.061-1.06l1.591-1.592a.75.75 0 0 1 1.06 0Z","clip-rule":"evenodd"})})})}),u&&m("div",{className:T("fixed",e.appearance.mode==="dark"&&"dark",e.appearance.position==="bottom-right"&&" bottom-24 right-6",e.appearance.position==="bottom-left"&&"bottom-24 left-6",e.appearance.position==="top-right"&&"right-6 top-24",e.appearance.position==="top-left"&&"left-6 top-24",e.appearance.position==="center"&&"left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform","border-border flex w-80 flex-col overflow-hidden rounded-lg border shadow-lg","transition-all duration-700 ease-in-out","h-[calc(100vh-200px)]"),children:m(Kt,{className:"h-[calc(100vh-200px)]",widget:e,widgetData:r,disabled:t||(i==null?void 0:i.isPreview),onTestWidget:o,onReset:a,canSubmit:n,size:"sm",onClose:()=>l(!0),error:c})})]})}function to({widgetId:e,apiEndpoint:r="/api/scrapagent",debounceMs:t=1500,onResponse:o,onError:n,domainPattern:a=/^(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?:\/.*)?$/,enabled:i=!0}){const[c,d]=R(!1),[l]=R({}),u=ue(new Map),s=ue(new Set),h=ue(null),f=(p,_)=>{};de(()=>{const _=i&&(()=>{const g=document.querySelector(`meta[name="widget-id"][content="${e}"]`),k=document.querySelector(`[data-widget-id="${e}"]`),E=document.querySelector(`script[data-widget-id="${e}"]`),N=window.WIDGET_ID===e;return!!(g||k||E||N)})();d(_)},[e,i]);const S=p=>{if(!p||typeof p!="string")return null;const _=p.trim();if(!_)return null;if(!a.test(_))return f("Domain pattern failed",{pattern:a.toString()}),null;let g=_.toLowerCase();return g=g.replace(/^https?:\/\//,""),g=g.replace(/^www\./,""),g=g.split("/")[0].split("?")[0].split("#")[0],g.length<3||!g.includes(".")?null:g},b=p=>{const _=l[p];return _?Date.now()-_.timestamp>_.ttl?(delete l[p],null):_.data:null},v=(p,_,g=3e5)=>{l[p]={data:_,timestamp:Date.now(),ttl:g}},w=async p=>{const _=b(p);if(_)return _;try{const g=`${r}?domain_url=${encodeURIComponent(p)}`;f("🔄 Making API call",{domain:p,apiUrl:g});const k=await fetch(g);if(f("API response received",{domain:p,status:k.status,statusText:k.statusText,ok:k.ok}),!k.ok)throw new Error(`API Error: ${k.status} ${k.statusText}`);const E=await k.json();return f("✅ API call successful",{domain:p,dataKeys:Object.keys(E)}),v(p,E),E}catch(g){throw f("❌ API call failed",{error:g instanceof Error?g.message:g}),g instanceof Error?g.message.includes("Failed to fetch")?new Error("Network error - Make sure your API at localhost:7071 is running."):g:new Error("Unknown error occurred while fetching domain data")}},C=async(p,_)=>{try{const g=await w(_);o==null||o(_,g);const k=new CustomEvent("domain-enriched",{detail:{domain:_,response:g,input:p},bubbles:!0});p.dispatchEvent(k),p.setAttribute("data-domain-response",JSON.stringify(g)),p.setAttribute("data-domain-enriched",_)}catch(g){const k=g instanceof Error?g.message:"Unknown error";n==null||n(_,k);const E=new CustomEvent("domain-enrichment-error",{detail:{domain:_,error:k,input:p},bubbles:!0});p.dispatchEvent(E),p.setAttribute("data-domain-error",k)}},z=p=>{const _=p.value,g=S(_);f("Input changed",{inputId:p.id||"no-id"});const k=u.current.get(p);if(k&&clearTimeout(k),p.removeAttribute("data-domain-response"),p.removeAttribute("data-domain-enriched"),p.removeAttribute("data-domain-error"),!g)return;const E=setTimeout(()=>{C(p,g),u.current.delete(p)},t);u.current.set(p,E)},M=p=>{const _=S(p.value);if(!_)return;const g=u.current.get(p);g&&(clearTimeout(g),u.current.delete(p)),C(p,_)},P=or(p=>{if(s.current.has(p))return;const _=()=>z(p),g=()=>M(p),k=E=>{E.key==="Enter"&&M(p)};p.addEventListener("input",_),p.addEventListener("blur",g),p.addEventListener("keydown",k),p.__headlessWidgetCleanup=()=>{p.removeEventListener("input",_),p.removeEventListener("blur",g),p.removeEventListener("keydown",k);const E=u.current.get(p);E&&(clearTimeout(E),u.current.delete(p))},s.current.add(p)},[]),B=or(()=>{if(!c)return;document.querySelectorAll('input[type="text"], input[type="url"], input[type="search"], input:not([type])').forEach(_=>{_ instanceof HTMLInputElement&&P(_)})},[c,P]);return de(()=>{if(c)return B(),h.current=new MutationObserver(p=>{p.forEach(_=>{_.addedNodes.forEach(g=>{var k;if(g.nodeType===Node.ELEMENT_NODE){const E=g;E.matches&&E.matches('input[type="text"], input[type="url"], input[type="search"], input:not([type])')&&P(E);const N=(k=E.querySelectorAll)==null?void 0:k.call(E,'input[type="text"], input[type="url"], input[type="search"], input:not([type])');N==null||N.forEach(oe=>{oe instanceof HTMLInputElement&&P(oe)})}})})}),h.current.observe(document.body,{childList:!0,subtree:!0}),()=>{var g;(g=h.current)==null||g.disconnect();const p=u.current,_=s.current;p.forEach(k=>clearTimeout(k)),p.clear(),_.forEach(k=>{var E;(E=k.__headlessWidgetCleanup)==null||E.call(k)}),_.clear()}},[c,t,P,B]),null}function oo(e){let r=e.apiUrl||"http://localhost:3000";return e.widgetId?m("div",{className:T(),children:m("div",{className:"font-sans theme-zinc bg-background text-foreground",children:[m(eo,{widgetId:e.widgetId,apiUrl:r,options:{isPreview:!1,openDelay:e.openDelay},verbose:e.verbose}),e.enableHeadlessWidget&&e.widgetId&&m(to,{widgetId:e.widgetId,apiEndpoint:e.headlessApiEndpoint||"/api/scrapagent",onResponse:(t,o)=>{e.verbose&&console.log(`[HeadlessWidget] Domain enriched: ${t}`,o)},onError:(t,o)=>{e.verbose&&console.error(`[HeadlessWidget] Error for ${t}:`,o)}})]})}):(console.error("[SaasRock.Widget] Widget ID is required"),null)}const no='*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]{display:none}:root{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--primary: 240 5.9% 10%;--primary-foreground: 0 0% 98%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--ring: 240 10% 3.9%;--radius: .5rem}.dark{--background: 240 10% 3.9%;--foreground: 0 0% 98%;--card: 240 10% 3.9%;--card-foreground: 0 0% 98%;--popover: 240 10% 3.9%;--popover-foreground: 0 0% 98%;--primary: 0 0% 98%;--primary-foreground: 240 5.9% 10%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--muted: 240 3.7% 15.9%;--muted-foreground: 240 5% 64.9%;--accent: 240 3.7% 15.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--ring: 240 4.9% 83.9%}.theme-zinc{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--primary: 222.2 84% 4.9%;--primary-foreground: 0 0% 98%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 240 5.9% 10%;--radius: .5rem}.dark .theme-zinc{--background: 240 10% 3.9%;--foreground: 0 0% 98%;--muted: 240 3.7% 15.9%;--muted-foreground: 240 5% 64.9%;--popover: 240 10% 3.9%;--popover-foreground: 0 0% 98%;--card: 240 10% 3.9%;--card-foreground: 0 0% 98%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--primary: 0 0% 98%;--primary-foreground: 240 5.9% 10%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--accent: 240 3.7% 15.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--ring: 240 4.9% 83.9%}.theme-slate{--background: 0 0% 100%;--foreground: 222.2 84% 4.9%;--muted: 210 40% 96.1%;--muted-foreground: 215.4 16.3% 46.9%;--popover: 0 0% 100%;--popover-foreground: 222.2 84% 4.9%;--card: 0 0% 100%;--card-foreground: 222.2 84% 4.9%;--border: 214.3 31.8% 91.4%;--input: 214.3 31.8% 91.4%;--primary: 222.2 47.4% 11.2%;--primary-foreground: 210 40% 98%;--secondary: 210 40% 96.1%;--secondary-foreground: 222.2 47.4% 11.2%;--accent: 210 40% 96.1%;--accent-foreground: 222.2 47.4% 11.2%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 40% 98%;--ring: 222.2 84% 4.9%;--radius: .5rem}.dark .theme-slate{--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--muted: 217.2 32.6% 17.5%;--muted-foreground: 215 20.2% 65.1%;--popover: 222.2 84% 4.9%;--popover-foreground: 210 40% 98%;--card: 222.2 84% 4.9%;--card-foreground: 210 40% 98%;--border: 217.2 32.6% 17.5%;--input: 217.2 32.6% 17.5%;--primary: 210 40% 98%;--primary-foreground: 222.2 47.4% 11.2%;--secondary: 217.2 32.6% 17.5%;--secondary-foreground: 210 40% 98%;--accent: 217.2 32.6% 17.5%;--accent-foreground: 210 40% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 40% 98%;--ring: 212.7 26.8% 83.9}.theme-stone{--background: 0 0% 100%;--foreground: 20 14.3% 4.1%;--muted: 60 4.8% 95.9%;--muted-foreground: 25 5.3% 44.7%;--popover: 0 0% 100%;--popover-foreground: 20 14.3% 4.1%;--card: 0 0% 100%;--card-foreground: 20 14.3% 4.1%;--border: 20 5.9% 90%;--input: 20 5.9% 90%;--primary: 24 9.8% 10%;--primary-foreground: 60 9.1% 97.8%;--secondary: 60 4.8% 95.9%;--secondary-foreground: 24 9.8% 10%;--accent: 60 4.8% 95.9%;--accent-foreground: 24 9.8% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 60 9.1% 97.8%;--ring: 20 14.3% 4.1%;--radius: .95rem}.dark .theme-stone{--background: 20 14.3% 4.1%;--foreground: 60 9.1% 97.8%;--muted: 12 6.5% 15.1%;--muted-foreground: 24 5.4% 63.9%;--popover: 20 14.3% 4.1%;--popover-foreground: 60 9.1% 97.8%;--card: 20 14.3% 4.1%;--card-foreground: 60 9.1% 97.8%;--border: 12 6.5% 15.1%;--input: 12 6.5% 15.1%;--primary: 60 9.1% 97.8%;--primary-foreground: 24 9.8% 10%;--secondary: 12 6.5% 15.1%;--secondary-foreground: 60 9.1% 97.8%;--accent: 12 6.5% 15.1%;--accent-foreground: 60 9.1% 97.8%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 60 9.1% 97.8%;--ring: 24 5.7% 82.9%}.theme-gray{--background: 0 0% 100%;--foreground: 224 71.4% 4.1%;--muted: 220 14.3% 95.9%;--muted-foreground: 220 8.9% 46.1%;--popover: 0 0% 100%;--popover-foreground: 224 71.4% 4.1%;--card: 0 0% 100%;--card-foreground: 224 71.4% 4.1%;--border: 220 13% 91%;--input: 220 13% 91%;--primary: 220.9 39.3% 11%;--primary-foreground: 210 20% 98%;--secondary: 220 14.3% 95.9%;--secondary-foreground: 220.9 39.3% 11%;--accent: 220 14.3% 95.9%;--accent-foreground: 220.9 39.3% 11%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 20% 98%;--ring: 224 71.4% 4.1%;--radius: .35rem}.dark .theme-gray{--background: 224 71.4% 4.1%;--foreground: 210 20% 98%;--muted: 215 27.9% 16.9%;--muted-foreground: 217.9 10.6% 64.9%;--popover: 224 71.4% 4.1%;--popover-foreground: 210 20% 98%;--card: 224 71.4% 4.1%;--card-foreground: 210 20% 98%;--border: 215 27.9% 16.9%;--input: 215 27.9% 16.9%;--primary: 210 20% 98%;--primary-foreground: 220.9 39.3% 11%;--secondary: 215 27.9% 16.9%;--secondary-foreground: 210 20% 98%;--accent: 215 27.9% 16.9%;--accent-foreground: 210 20% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 20% 98%;--ring: 216 12.2% 83.9%}.theme-neutral{--background: 0 0% 100%;--foreground: 0 0% 3.9%;--muted: 0 0% 96.1%;--muted-foreground: 0 0% 45.1%;--popover: 0 0% 100%;--popover-foreground: 0 0% 3.9%;--card: 0 0% 100%;--card-foreground: 0 0% 3.9%;--border: 0 0% 89.8%;--input: 0 0% 89.8%;--primary: 0 0% 9%;--primary-foreground: 0 0% 98%;--secondary: 0 0% 96.1%;--secondary-foreground: 0 0% 9%;--accent: 0 0% 96.1%;--accent-foreground: 0 0% 9%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 0 0% 3.9%;--radius: .6rem}.dark .theme-neutral{--background: 0 0% 3.9%;--foreground: 0 0% 98%;--muted: 0 0% 14.9%;--muted-foreground: 0 0% 63.9%;--popover: 0 0% 3.9%;--popover-foreground: 0 0% 98%;--card: 0 0% 3.9%;--card-foreground: 0 0% 98%;--border: 0 0% 14.9%;--input: 0 0% 14.9%;--primary: 0 0% 98%;--primary-foreground: 0 0% 9%;--secondary: 0 0% 14.9%;--secondary-foreground: 0 0% 98%;--accent: 0 0% 14.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--ring: 0 0% 83.1%}.theme-red{--background: 0 0% 100%;--foreground: 0 0% 3.9%;--muted: 0 0% 96.1%;--muted-foreground: 0 0% 45.1%;--popover: 0 0% 100%;--popover-foreground: 0 0% 3.9%;--card: 0 0% 100%;--card-foreground: 0 0% 3.9%;--border: 0 0% 89.8%;--input: 0 0% 89.8%;--primary: 0 72.2% 50.6%;--primary-foreground: 0 85.7% 97.3%;--secondary: 0 0% 96.1%;--secondary-foreground: 0 0% 9%;--accent: 0 0% 96.1%;--accent-foreground: 0 0% 9%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 0 72.2% 50.6%;--radius: .4rem}.dark .theme-red{--background: 0 0% 3.9%;--foreground: 0 0% 98%;--muted: 0 0% 14.9%;--muted-foreground: 0 0% 63.9%;--popover: 0 0% 3.9%;--popover-foreground: 0 0% 98%;--card: 0 0% 3.9%;--card-foreground: 0 0% 98%;--border: 0 0% 14.9%;--input: 0 0% 14.9%;--primary: 0 72.2% 50.6%;--primary-foreground: 0 85.7% 97.3%;--secondary: 0 0% 14.9%;--secondary-foreground: 0 0% 98%;--accent: 0 0% 14.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--ring: 0 72.2% 50.6%}.theme-rose{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--primary: 346.8 77.2% 49.8%;--primary-foreground: 355.7 100% 97.3%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 346.8 77.2% 49.8%;--radius: .5rem}.dark .theme-rose{--background: 20 14.3% 4.1%;--foreground: 0 0% 95%;--muted: 0 0% 15%;--muted-foreground: 240 5% 64.9%;--popover: 0 0% 9%;--popover-foreground: 0 0% 95%;--card: 24 9.8% 10%;--card-foreground: 0 0% 95%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--primary: 346.8 77.2% 49.8%;--primary-foreground: 355.7 100% 97.3%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--accent: 12 6.5% 15.1%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 85.7% 97.3%;--ring: 346.8 77.2% 49.8%}.theme-orange{--background: 0 0% 100%;--foreground: 20 14.3% 4.1%;--muted: 60 4.8% 95.9%;--muted-foreground: 25 5.3% 44.7%;--popover: 0 0% 100%;--popover-foreground: 20 14.3% 4.1%;--card: 0 0% 100%;--card-foreground: 20 14.3% 4.1%;--border: 20 5.9% 90%;--input: 20 5.9% 90%;--primary: 24.6 95% 53.1%;--primary-foreground: 60 9.1% 97.8%;--secondary: 60 4.8% 95.9%;--secondary-foreground: 24 9.8% 10%;--accent: 60 4.8% 95.9%;--accent-foreground: 24 9.8% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 60 9.1% 97.8%;--ring: 24.6 95% 53.1%;--radius: .95rem}.dark .theme-orange{--background: 20 14.3% 4.1%;--foreground: 60 9.1% 97.8%;--muted: 12 6.5% 15.1%;--muted-foreground: 24 5.4% 63.9%;--popover: 20 14.3% 4.1%;--popover-foreground: 60 9.1% 97.8%;--card: 20 14.3% 4.1%;--card-foreground: 60 9.1% 97.8%;--border: 12 6.5% 15.1%;--input: 12 6.5% 15.1%;--primary: 20.5 90.2% 48.2%;--primary-foreground: 60 9.1% 97.8%;--secondary: 12 6.5% 15.1%;--secondary-foreground: 60 9.1% 97.8%;--accent: 12 6.5% 15.1%;--accent-foreground: 60 9.1% 97.8%;--destructive: 0 72.2% 50.6%;--destructive-foreground: 60 9.1% 97.8%;--ring: 20.5 90.2% 48.2%}.theme-green{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--primary: 142.1 76.2% 36.3%;--primary-foreground: 355.7 100% 97.3%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--ring: 142.1 76.2% 36.3%;--radius: .6rem}.dark .theme-green{--background: 20 14.3% 4.1%;--foreground: 0 0% 95%;--muted: 0 0% 15%;--muted-foreground: 240 5% 64.9%;--popover: 0 0% 9%;--popover-foreground: 0 0% 95%;--card: 24 9.8% 10%;--card-foreground: 0 0% 95%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--primary: 142.1 70.6% 45.3%;--primary-foreground: 144.9 80.4% 10%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--accent: 12 6.5% 15.1%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 85.7% 97.3%;--ring: 142.4 71.8% 29.2%}.theme-blue{--background: 0 0% 100%;--foreground: 222.2 84% 4.9%;--muted: 210 40% 96.1%;--muted-foreground: 215.4 16.3% 46.9%;--popover: 0 0% 100%;--popover-foreground: 222.2 84% 4.9%;--card: 0 0% 100%;--card-foreground: 222.2 84% 4.9%;--border: 214.3 31.8% 91.4%;--input: 214.3 31.8% 91.4%;--primary: 221.2 83.2% 53.3%;--primary-foreground: 210 40% 98%;--secondary: 210 40% 96.1%;--secondary-foreground: 222.2 47.4% 11.2%;--accent: 210 40% 96.1%;--accent-foreground: 222.2 47.4% 11.2%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 40% 98%;--ring: 221.2 83.2% 53.3%;--radius: .6rem}.dark .theme-blue{--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--muted: 217.2 32.6% 17.5%;--muted-foreground: 215 20.2% 65.1%;--popover: 222.2 84% 4.9%;--popover-foreground: 210 40% 98%;--card: 222.2 84% 4.9%;--card-foreground: 210 40% 98%;--border: 217.2 32.6% 17.5%;--input: 217.2 32.6% 17.5%;--primary: 217.2 91.2% 59.8%;--primary-foreground: 222.2 47.4% 11.2%;--secondary: 217.2 32.6% 17.5%;--secondary-foreground: 210 40% 98%;--accent: 217.2 32.6% 17.5%;--accent-foreground: 210 40% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 40% 98%;--ring: 224.3 76.3% 48%}.theme-yellow{--gradient: #faf972;--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--muted: 63 46.5% 21.299999999999997%;--muted-foreground: 63 9.3% 57.1%;--popover: 63 46.4% 9.23%;--popover-foreground: 63 9.3% 98.55%;--card: 63 46.4% 9.23%;--card-foreground: 63 9.3% 98.55%;--border: 63 46.5% 21.299999999999997%;--input: 63 46.5% 21.299999999999997%;--primary: 63 93% 71%;--primary-foreground: 63 9.3% 7.1%;--secondary: 63 46.5% 21.299999999999997%;--secondary-foreground: 63 9.3% 98.55%;--accent: 63 46.5% 21.299999999999997%;--accent-foreground: 63 9.3% 98.55%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 63 9.3% 98.55%;--ring: 63 93% 71%}.dark .theme-yellow{--background: 20 14.3% 4.1%;--foreground: 60 9.1% 97.8%;--muted: 12 6.5% 15.1%;--muted-foreground: 24 5.4% 63.9%;--popover: 20 14.3% 4.1%;--popover-foreground: 60 9.1% 97.8%;--card: 20 14.3% 4.1%;--card-foreground: 60 9.1% 97.8%;--border: 12 6.5% 15.1%;--input: 12 6.5% 15.1%;--primary: 47.9 95.8% 53.1%;--primary-foreground: 26 83.3% 14.1%;--secondary: 12 6.5% 15.1%;--secondary-foreground: 60 9.1% 97.8%;--accent: 12 6.5% 15.1%;--accent-foreground: 60 9.1% 97.8%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 60 9.1% 97.8%;--ring: 35.5 91.7% 32.9%}.theme-violet{--background: 0 0% 100%;--foreground: 224 71.4% 4.1%;--muted: 220 14.3% 95.9%;--muted-foreground: 220 8.9% 46.1%;--popover: 0 0% 100%;--popover-foreground: 224 71.4% 4.1%;--card: 0 0% 100%;--card-foreground: 224 71.4% 4.1%;--border: 220 13% 91%;--input: 220 13% 91%;--primary: 262.1 83.3% 57.8%;--primary-foreground: 210 20% 98%;--secondary: 220 14.3% 95.9%;--secondary-foreground: 220.9 39.3% 11%;--accent: 220 14.3% 95.9%;--accent-foreground: 220.9 39.3% 11%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 20% 98%;--ring: 262.1 83.3% 57.8%;--radius: .6rem}.dark .theme-violet{--background: 224 71.4% 4.1%;--foreground: 210 20% 98%;--muted: 215 27.9% 16.9%;--muted-foreground: 217.9 10.6% 64.9%;--popover: 224 71.4% 4.1%;--popover-foreground: 210 20% 98%;--card: 224 71.4% 4.1%;--card-foreground: 210 20% 98%;--border: 215 27.9% 16.9%;--input: 215 27.9% 16.9%;--primary: 263.4 70% 50.4%;--primary-foreground: 210 20% 98%;--secondary: 215 27.9% 16.9%;--secondary-foreground: 210 20% 98%;--accent: 215 27.9% 16.9%;--accent-foreground: 210 20% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 20% 98%;--ring: 263.4 70% 50.4%}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.\\!visible{visibility:visible!important}.visible{visibility:visible}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-y-0{top:0;bottom:0}.bottom-24{bottom:6rem}.bottom-6{bottom:1.5rem}.left-1\\/2{left:50%}.left-6{left:1.5rem}.right-1{right:.25rem}.right-6{right:1.5rem}.top-1{top:.25rem}.top-1\\/2{top:50%}.top-24{top:6rem}.top-6{top:1.5rem}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.ml-3{margin-left:.75rem}.mt-2{margin-top:.5rem}.inline{display:inline}.flex{display:flex}.inline-flex{display:inline-flex}.hidden{display:none}.h-10{height:2.5rem}.h-11{height:2.75rem}.h-14{height:3.5rem}.h-4{height:1rem}.h-5{height:1.25rem}.h-7{height:1.75rem}.h-8{height:2rem}.h-9{height:2.25rem}.h-\\[calc\\(100vh-100px\\)\\]{height:calc(100vh - 100px)}.h-\\[calc\\(100vh-200px\\)\\]{height:calc(100vh - 200px)}.w-14{width:3.5rem}.w-4{width:1rem}.w-5{width:1.25rem}.w-7{width:1.75rem}.w-8{width:2rem}.w-80{width:20rem}.w-9{width:2.25rem}.w-full{width:100%}.flex-1{flex:1 1 0%}.flex-shrink-0{flex-shrink:0}.flex-grow{flex-grow:1}.-translate-x-1\\/2{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\\/2{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.whitespace-nowrap{white-space:nowrap}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.border{border-width:1px}.border-b{border-bottom-width:1px}.border-border{border-color:hsl(var(--border))}.border-input{border-color:hsl(var(--input))}.border-red-300{--tw-border-opacity: 1;border-color:rgb(252 165 165 / var(--tw-border-opacity))}.bg-background{background-color:hsl(var(--background))}.bg-destructive{background-color:hsl(var(--destructive))}.bg-primary{background-color:hsl(var(--primary))}.bg-red-50{--tw-bg-opacity: 1;background-color:rgb(254 242 242 / var(--tw-bg-opacity))}.bg-secondary{background-color:hsl(var(--secondary))}.bg-transparent{background-color:transparent}.object-contain{-o-object-fit:contain;object-fit:contain}.p-1{padding:.25rem}.p-4{padding:1rem}.px-1{padding-left:.25rem;padding-right:.25rem}.px-2{padding-left:.5rem;padding-right:.5rem}.px-3{padding-left:.75rem;padding-right:.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-8{padding-left:2rem;padding-right:2rem}.py-1{padding-top:.25rem;padding-bottom:.25rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.pb-2{padding-bottom:.5rem}.text-center{text-align:center}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji"}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.leading-5{line-height:1.25rem}.text-destructive-foreground{color:hsl(var(--destructive-foreground))}.text-foreground{color:hsl(var(--foreground))}.text-primary{color:hsl(var(--primary))}.text-primary-foreground{color:hsl(var(--primary-foreground))}.text-red-400{--tw-text-opacity: 1;color:rgb(248 113 113 / var(--tw-text-opacity))}.text-red-700{--tw-text-opacity: 1;color:rgb(185 28 28 / var(--tw-text-opacity))}.text-red-800{--tw-text-opacity: 1;color:rgb(153 27 27 / var(--tw-text-opacity))}.text-secondary-foreground{color:hsl(var(--secondary-foreground))}.underline-offset-4{text-underline-offset:4px}.shadow{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-md{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-sm{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.outline{outline-style:solid}.ring-offset-background{--tw-ring-offset-color: hsl(var(--background))}.blur{--tw-blur: blur(8px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-300{transition-duration:.3s}.duration-700{transition-duration:.7s}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.file\\:border-0::file-selector-button{border-width:0px}.file\\:bg-transparent::file-selector-button{background-color:transparent}.file\\:text-sm::file-selector-button{font-size:.875rem;line-height:1.25rem}.file\\:font-medium::file-selector-button{font-weight:500}.placeholder\\:text-muted-foreground::-moz-placeholder{color:hsl(var(--muted-foreground))}.placeholder\\:text-muted-foreground::placeholder{color:hsl(var(--muted-foreground))}.hover\\:bg-accent:hover{background-color:hsl(var(--accent))}.hover\\:bg-destructive\\/90:hover{background-color:hsl(var(--destructive) / .9)}.hover\\:bg-primary\\/90:hover{background-color:hsl(var(--primary) / .9)}.hover\\:bg-secondary\\/80:hover{background-color:hsl(var(--secondary) / .8)}.hover\\:text-accent-foreground:hover{color:hsl(var(--accent-foreground))}.hover\\:underline:hover{text-decoration-line:underline}.focus-visible\\:outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.focus-visible\\:ring-1:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus-visible\\:ring-2:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus-visible\\:ring-ring:focus-visible{--tw-ring-color: hsl(var(--ring))}.focus-visible\\:ring-offset-2:focus-visible{--tw-ring-offset-width: 2px}.disabled\\:pointer-events-none:disabled{pointer-events:none}.disabled\\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\\:opacity-50:disabled{opacity:.5}:is(.dark .dark\\:border-red-700){--tw-border-opacity: 1;border-color:rgb(185 28 28 / var(--tw-border-opacity))}:is(.dark .dark\\:bg-red-900){--tw-bg-opacity: 1;background-color:rgb(127 29 29 / var(--tw-bg-opacity))}:is(.dark .dark\\:text-red-100){--tw-text-opacity: 1;color:rgb(254 226 226 / var(--tw-text-opacity))}:is(.dark .dark\\:text-red-300){--tw-text-opacity: 1;color:rgb(252 165 165 / var(--tw-text-opacity))}@media (min-width: 1024px){.lg\\:max-w-\\[40rem\\]{max-width:40rem}}@media (min-width: 1280px){.xl\\:max-w-\\[48rem\\]{max-width:48rem}}';function ao(e){const r=document.createElement("style");r.textContent=no,e.appendChild(r)}function io(e){const r=document.createElement("div"),t=r.attachShadow({mode:"open"});document.body.appendChild(r),ao(t),Gr(m(oo,{...e}),t)}{const e=document.currentScript;if(e){const r=((jr=e.getAttribute("src"))==null?void 0:jr.toString())??"",t=new URL(r);console.log({src:r,url:t});const o=e.getAttribute("data-api-url"),n=e.getAttribute("data-widget-id"),a=e.getAttribute("data-verbose")==="true";let i=-1;e.hasAttribute("data-open-delay")&&(i=parseInt(e.getAttribute("data-open-delay")||"-1")),a&&console.log("[SaasRock.Widget] Loading script...",{apiUrl:o,widgetId:n,openDelay:i}),window.Widget={init:c=>{io(c)}},window.Widget.init({apiUrl:o,widgetId:n,verbose:a,openDelay:i})}}});
